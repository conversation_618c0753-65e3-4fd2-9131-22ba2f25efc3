-- =====================================================
-- AMIS DATABASE SCHEMA WITH SAMPLE DATA
-- Agricultural Management Information System Database
-- Generated on: 2025-08-09
-- Database: amis_db
-- Note: No foreign key constraints as requested
-- =====================================================

-- Drop database if exists and create new
DROP DATABASE IF EXISTS `amis_db`;
CREATE DATABASE `amis_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `amis_db`;

-- =====================================================
-- ADMINISTRATIVE/REFERENCE TABLES
-- =====================================================

-- Countries table
CREATE TABLE `adx_country` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Government structure table
CREATE TABLE `gov_structure` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `level` varchar(50) DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Regions table
CREATE TABLE `regions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(20) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Region province link table
CREATE TABLE `region_province_link` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `region_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- USER AND ORGANIZATION MANAGEMENT
-- =====================================================

-- System users table
CREATE TABLE `dakoii_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Application users table
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `role` varchar(50) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Branches table
CREATE TABLE `branches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(50) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `manager_id` int(11) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Organization settings table
CREATE TABLE `org_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SMALL AND MEDIUM ENTERPRISES (SME) MANAGEMENT
-- =====================================================

-- SME table
CREATE TABLE `sme` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `business_name` varchar(255) NOT NULL,
  `registration_number` varchar(100) DEFAULT NULL,
  `business_type` varchar(100) DEFAULT NULL,
  `industry_sector` varchar(100) DEFAULT NULL,
  `owner_name` varchar(255) DEFAULT NULL,
  `contact_phone` varchar(20) DEFAULT NULL,
  `contact_email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `establishment_date` date DEFAULT NULL,
  `employee_count` int(11) DEFAULT NULL,
  `annual_revenue` decimal(15,2) DEFAULT NULL,
  `status` enum('active','inactive','suspended','closed') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- SME staff table
CREATE TABLE `sme_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sme_id` int(11) NOT NULL,
  `staff_name` varchar(255) NOT NULL,
  `position` varchar(100) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `contact_phone` varchar(20) DEFAULT NULL,
  `contact_email` varchar(255) DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `employment_type` enum('full_time','part_time','contract','casual') DEFAULT 'full_time',
  `status` enum('active','inactive','terminated','resigned') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- DOCUMENT MANAGEMENT
-- =====================================================

-- Folders table
CREATE TABLE `folders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `path` varchar(500) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Documents table
CREATE TABLE `documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `file_path` varchar(500) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `folder_id` int(11) DEFAULT NULL,
  `document_type` varchar(100) DEFAULT NULL,
  `version` varchar(20) DEFAULT '1.0',
  `uploaded_by` int(11) DEFAULT NULL,
  `status` enum('draft','active','archived','deleted') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- COMMODITIES AND MARKET DATA
-- =====================================================

-- Commodities table
CREATE TABLE `commodities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `commodity_code` varchar(50) DEFAULT NULL,
  `category` varchar(100) DEFAULT NULL,
  `subcategory` varchar(100) DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `seasonal_pattern` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive','discontinued') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `commodity_code` (`commodity_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Commodity prices table
CREATE TABLE `commodity_prices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `commodity_id` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `currency` varchar(10) DEFAULT 'PGK',
  `market_location` varchar(255) DEFAULT NULL,
  `price_date` date NOT NULL,
  `price_type` enum('wholesale','retail','farm_gate','export') DEFAULT 'retail',
  `quality_grade` varchar(50) DEFAULT NULL,
  `source` varchar(100) DEFAULT NULL,
  `verified_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Commodity production table
CREATE TABLE `commodity_production` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `commodity_id` int(11) NOT NULL,
  `production_quantity` decimal(15,2) NOT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `production_period` varchar(20) DEFAULT NULL,
  `production_year` year(4) NOT NULL,
  `production_season` varchar(50) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `province_id` int(11) DEFAULT NULL,
  `area_harvested` decimal(10,2) DEFAULT NULL,
  `yield_per_hectare` decimal(10,2) DEFAULT NULL,
  `source` varchar(100) DEFAULT NULL,
  `data_quality` enum('estimated','surveyed','census','administrative') DEFAULT 'estimated',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- MEETING AND AGREEMENT MANAGEMENT
-- =====================================================

-- Meetings table
CREATE TABLE `meetings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `meeting_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `meeting_date` datetime NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `organizer_id` int(11) DEFAULT NULL,
  `agenda` text DEFAULT NULL,
  `minutes` text DEFAULT NULL,
  `attendees_count` int(11) DEFAULT NULL,
  `status` enum('scheduled','ongoing','completed','cancelled','postponed') DEFAULT 'scheduled',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Agreements table
CREATE TABLE `agreements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `agreement_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `parties_involved` text DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `value_amount` decimal(15,2) DEFAULT NULL,
  `currency` varchar(10) DEFAULT 'PGK',
  `terms_conditions` text DEFAULT NULL,
  `responsible_officer_id` int(11) DEFAULT NULL,
  `status` enum('draft','negotiation','signed','active','completed','terminated') DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Proposals table
CREATE TABLE `proposal` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `proposal_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `objectives` text DEFAULT NULL,
  `budget_requested` decimal(15,2) DEFAULT NULL,
  `currency` varchar(10) DEFAULT 'PGK',
  `duration_months` int(11) DEFAULT NULL,
  `submitted_by` int(11) DEFAULT NULL,
  `submitted_date` date DEFAULT NULL,
  `reviewed_by` int(11) DEFAULT NULL,
  `review_date` date DEFAULT NULL,
  `review_comments` text DEFAULT NULL,
  `status` enum('draft','submitted','under_review','approved','rejected','implemented') DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- PLANNING AND DEVELOPMENT TABLES
-- =====================================================

-- Corporate plans table
CREATE TABLE `plans_corporate_plan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plan_name` varchar(255) NOT NULL,
  `plan_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `start_year` year(4) NOT NULL,
  `end_year` year(4) NOT NULL,
  `vision` text DEFAULT NULL,
  `mission` text DEFAULT NULL,
  `strategic_goals` text DEFAULT NULL,
  `budget_allocation` decimal(15,2) DEFAULT NULL,
  `responsible_department` varchar(255) DEFAULT NULL,
  `status` enum('draft','approved','active','completed','cancelled') DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `plan_code` (`plan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Medium Term Development Plans table
CREATE TABLE `plans_mtdp` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plan_name` varchar(255) NOT NULL,
  `plan_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `start_year` year(4) NOT NULL,
  `end_year` year(4) NOT NULL,
  `development_priorities` text DEFAULT NULL,
  `budget_allocation` decimal(15,2) DEFAULT NULL,
  `implementing_agency` varchar(255) DEFAULT NULL,
  `status` enum('draft','approved','active','completed','cancelled') DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `plan_code` (`plan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- MTDP District Implementation Plans table
CREATE TABLE `plans_mtdp_dip` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mtdp_id` int(11) NOT NULL,
  `district_name` varchar(255) NOT NULL,
  `district_code` varchar(50) DEFAULT NULL,
  `implementation_strategy` text DEFAULT NULL,
  `local_priorities` text DEFAULT NULL,
  `budget_allocation` decimal(15,2) DEFAULT NULL,
  `responsible_officer` varchar(255) DEFAULT NULL,
  `status` enum('draft','approved','active','completed') DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- MTDP Key Result Areas table
CREATE TABLE `plans_mtdp_kra` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mtdp_id` int(11) NOT NULL,
  `kra_name` varchar(255) NOT NULL,
  `kra_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `target_outcome` text DEFAULT NULL,
  `budget_allocation` decimal(15,2) DEFAULT NULL,
  `responsible_department` varchar(255) DEFAULT NULL,
  `priority_level` enum('high','medium','low') DEFAULT 'medium',
  `status` enum('planned','active','completed','cancelled') DEFAULT 'planned',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- MTDP Strategic Priority Areas table
CREATE TABLE `plans_mtdp_spa` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mtdp_id` int(11) NOT NULL,
  `spa_name` varchar(255) NOT NULL,
  `spa_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `strategic_objectives` text DEFAULT NULL,
  `budget_allocation` decimal(15,2) DEFAULT NULL,
  `implementing_partners` text DEFAULT NULL,
  `priority_ranking` int(11) DEFAULT NULL,
  `status` enum('planned','active','completed','cancelled') DEFAULT 'planned',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- MTDP Specific Areas table
CREATE TABLE `plans_mtdp_specific_area` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `spa_id` int(11) NOT NULL,
  `area_name` varchar(255) NOT NULL,
  `area_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `specific_targets` text DEFAULT NULL,
  `budget_allocation` decimal(15,2) DEFAULT NULL,
  `timeline` varchar(255) DEFAULT NULL,
  `responsible_unit` varchar(255) DEFAULT NULL,
  `status` enum('planned','active','completed','cancelled') DEFAULT 'planned',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- MTDP Strategies table
CREATE TABLE `plans_mtdp_strategies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mtdp_id` int(11) NOT NULL,
  `strategy_name` varchar(255) NOT NULL,
  `strategy_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `implementation_approach` text DEFAULT NULL,
  `expected_outcomes` text DEFAULT NULL,
  `budget_requirement` decimal(15,2) DEFAULT NULL,
  `timeline` varchar(255) DEFAULT NULL,
  `risk_factors` text DEFAULT NULL,
  `status` enum('planned','active','completed','cancelled') DEFAULT 'planned',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- MTDP Indicators table
CREATE TABLE `plans_mtdp_indicators` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mtdp_id` int(11) NOT NULL,
  `indicator_name` varchar(255) NOT NULL,
  `indicator_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `measurement_unit` varchar(100) DEFAULT NULL,
  `baseline_value` decimal(15,2) DEFAULT NULL,
  `target_value` decimal(15,2) DEFAULT NULL,
  `current_value` decimal(15,2) DEFAULT NULL,
  `data_source` varchar(255) DEFAULT NULL,
  `collection_frequency` varchar(100) DEFAULT NULL,
  `responsible_agency` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive','achieved','discontinued') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- MTDP Investments table
CREATE TABLE `plans_mtdp_investments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mtdp_id` int(11) NOT NULL,
  `investment_name` varchar(255) NOT NULL,
  `investment_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `investment_type` varchar(100) DEFAULT NULL,
  `sector` varchar(100) DEFAULT NULL,
  `total_cost` decimal(15,2) DEFAULT NULL,
  `funding_source` varchar(255) DEFAULT NULL,
  `implementation_period` varchar(100) DEFAULT NULL,
  `expected_benefits` text DEFAULT NULL,
  `implementing_agency` varchar(255) DEFAULT NULL,
  `status` enum('planned','approved','ongoing','completed','cancelled') DEFAULT 'planned',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- National Agriculture Sector Plans table
CREATE TABLE `plans_nasp` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plan_name` varchar(255) NOT NULL,
  `plan_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `start_year` year(4) NOT NULL,
  `end_year` year(4) NOT NULL,
  `sector_priorities` text DEFAULT NULL,
  `strategic_themes` text DEFAULT NULL,
  `budget_allocation` decimal(15,2) DEFAULT NULL,
  `implementing_ministry` varchar(255) DEFAULT NULL,
  `coordination_mechanism` text DEFAULT NULL,
  `monitoring_framework` text DEFAULT NULL,
  `status` enum('draft','approved','active','completed','cancelled') DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `plan_code` (`plan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- WORKPLAN MANAGEMENT TABLES
-- =====================================================

-- Workplans table
CREATE TABLE `workplans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workplan_name` varchar(255) NOT NULL,
  `workplan_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `workplan_type` varchar(100) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `budget_allocated` decimal(15,2) DEFAULT NULL,
  `budget_spent` decimal(15,2) DEFAULT NULL,
  `responsible_officer_id` int(11) DEFAULT NULL,
  `department` varchar(255) DEFAULT NULL,
  `priority_level` enum('high','medium','low') DEFAULT 'medium',
  `status` enum('draft','approved','active','completed','cancelled','suspended') DEFAULT 'draft',
  `created_by` int(11) DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `approved_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workplan_code` (`workplan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Workplan activities table
CREATE TABLE `workplan_activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workplan_id` int(11) NOT NULL,
  `activity_name` varchar(255) NOT NULL,
  `activity_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `activity_type` varchar(100) DEFAULT NULL,
  `planned_start_date` date DEFAULT NULL,
  `planned_end_date` date DEFAULT NULL,
  `actual_start_date` date DEFAULT NULL,
  `actual_end_date` date DEFAULT NULL,
  `budget_allocated` decimal(15,2) DEFAULT NULL,
  `budget_spent` decimal(15,2) DEFAULT NULL,
  `responsible_person` varchar(255) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `expected_output` text DEFAULT NULL,
  `actual_output` text DEFAULT NULL,
  `status` enum('planned','ongoing','completed','cancelled','delayed') DEFAULT 'planned',
  `completion_percentage` decimal(5,2) DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Workplan infrastructure activities table
CREATE TABLE `workplan_infrastructure_activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workplan_id` int(11) NOT NULL,
  `infrastructure_name` varchar(255) NOT NULL,
  `infrastructure_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `planned_start_date` date DEFAULT NULL,
  `planned_completion_date` date DEFAULT NULL,
  `actual_start_date` date DEFAULT NULL,
  `actual_completion_date` date DEFAULT NULL,
  `budget_allocated` decimal(15,2) DEFAULT NULL,
  `budget_spent` decimal(15,2) DEFAULT NULL,
  `contractor` varchar(255) DEFAULT NULL,
  `project_manager` varchar(255) DEFAULT NULL,
  `technical_specifications` text DEFAULT NULL,
  `beneficiaries` text DEFAULT NULL,
  `status` enum('planned','tendering','construction','completed','cancelled') DEFAULT 'planned',
  `completion_percentage` decimal(5,2) DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Workplan input activities table
CREATE TABLE `workplan_input_activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workplan_id` int(11) NOT NULL,
  `input_name` varchar(255) NOT NULL,
  `input_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `quantity_planned` decimal(15,2) DEFAULT NULL,
  `quantity_delivered` decimal(15,2) DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `unit_cost` decimal(10,2) DEFAULT NULL,
  `total_cost` decimal(15,2) DEFAULT NULL,
  `supplier` varchar(255) DEFAULT NULL,
  `delivery_location` varchar(255) DEFAULT NULL,
  `planned_delivery_date` date DEFAULT NULL,
  `actual_delivery_date` date DEFAULT NULL,
  `quality_status` enum('excellent','good','satisfactory','poor') DEFAULT NULL,
  `beneficiaries` text DEFAULT NULL,
  `status` enum('planned','procured','delivered','distributed','completed') DEFAULT 'planned',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Workplan output activities table
CREATE TABLE `workplan_output_activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workplan_id` int(11) NOT NULL,
  `output_name` varchar(255) NOT NULL,
  `output_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `target_quantity` decimal(15,2) DEFAULT NULL,
  `achieved_quantity` decimal(15,2) DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `target_beneficiaries` int(11) DEFAULT NULL,
  `actual_beneficiaries` int(11) DEFAULT NULL,
  `quality_indicators` text DEFAULT NULL,
  `delivery_timeline` varchar(255) DEFAULT NULL,
  `responsible_unit` varchar(255) DEFAULT NULL,
  `monitoring_mechanism` text DEFAULT NULL,
  `challenges` text DEFAULT NULL,
  `lessons_learned` text DEFAULT NULL,
  `status` enum('planned','ongoing','achieved','partially_achieved','not_achieved') DEFAULT 'planned',
  `achievement_percentage` decimal(5,2) DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Workplan training activities table
CREATE TABLE `workplan_training_activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workplan_id` int(11) NOT NULL,
  `training_name` varchar(255) NOT NULL,
  `training_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `target_participants` int(11) DEFAULT NULL,
  `actual_participants` int(11) DEFAULT NULL,
  `training_duration_days` int(11) DEFAULT NULL,
  `venue` varchar(255) DEFAULT NULL,
  `trainer_name` varchar(255) DEFAULT NULL,
  `training_materials` text DEFAULT NULL,
  `planned_start_date` date DEFAULT NULL,
  `planned_end_date` date DEFAULT NULL,
  `actual_start_date` date DEFAULT NULL,
  `actual_end_date` date DEFAULT NULL,
  `budget_allocated` decimal(15,2) DEFAULT NULL,
  `budget_spent` decimal(15,2) DEFAULT NULL,
  `learning_objectives` text DEFAULT NULL,
  `evaluation_results` text DEFAULT NULL,
  `status` enum('planned','ongoing','completed','cancelled','postponed') DEFAULT 'planned',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- WORKPLAN LINKING TABLES
-- =====================================================

-- Workplan corporate plan link table
CREATE TABLE `workplan_corporate_plan_link` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workplan_id` int(11) NOT NULL,
  `corporate_plan_id` int(11) NOT NULL,
  `alignment_description` text DEFAULT NULL,
  `contribution_level` enum('high','medium','low') DEFAULT 'medium',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Workplan MTDP link table
CREATE TABLE `workplan_mtdp_link` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workplan_id` int(11) NOT NULL,
  `mtdp_id` int(11) NOT NULL,
  `kra_id` int(11) DEFAULT NULL,
  `spa_id` int(11) DEFAULT NULL,
  `alignment_description` text DEFAULT NULL,
  `contribution_level` enum('high','medium','low') DEFAULT 'medium',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Workplan NASP link table
CREATE TABLE `workplan_nasp_link` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workplan_id` int(11) NOT NULL,
  `nasp_id` int(11) NOT NULL,
  `alignment_description` text DEFAULT NULL,
  `sector_contribution` varchar(255) DEFAULT NULL,
  `contribution_level` enum('high','medium','low') DEFAULT 'medium',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Workplan others link table
CREATE TABLE `workplan_others_link` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workplan_id` int(11) NOT NULL,
  `plan_name` varchar(255) NOT NULL,
  `plan_type` varchar(100) DEFAULT NULL,
  `plan_reference` varchar(255) DEFAULT NULL,
  `alignment_description` text DEFAULT NULL,
  `contribution_level` enum('high','medium','low') DEFAULT 'medium',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SYSTEM MANAGEMENT
-- =====================================================

-- Migrations table
CREATE TABLE `migrations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert sample countries
INSERT INTO `adx_country` (`name`, `code`) VALUES
('Papua New Guinea', 'PG'),
('Australia', 'AU'),
('Solomon Islands', 'SB'),
('Vanuatu', 'VU'),
('Fiji', 'FJ');

-- Insert sample regions
INSERT INTO `regions` (`name`, `code`, `description`) VALUES
('Highlands Region', 'HR', 'Highland provinces of PNG'),
('Islands Region', 'IR', 'Island provinces of PNG'),
('Momase Region', 'MR', 'Morobe, Madang, Sepik provinces'),
('Southern Region', 'SR', 'Southern provinces of PNG');

-- Insert sample government structure
INSERT INTO `gov_structure` (`name`, `level`, `parent_id`) VALUES
('Papua New Guinea', 'national', NULL),
('National Capital District', 'provincial', 1),
('Central Province', 'provincial', 1),
('Western Province', 'provincial', 1),
('Gulf Province', 'provincial', 1),
('Port Moresby', 'district', 2),
('Abau', 'district', 3),
('Daru', 'district', 4),
('Kerema', 'district', 5);

-- Insert sample system users
INSERT INTO `dakoii_users` (`username`, `email`, `password_hash`, `first_name`, `last_name`, `phone`, `status`) VALUES
('admin', '<EMAIL>', '$2y$10$example_hash_1', 'System', 'Administrator', '+************', 'active'),
('planner1', '<EMAIL>', '$2y$10$example_hash_2', 'John', 'Planner', '+************', 'active'),
('manager1', '<EMAIL>', '$2y$10$example_hash_3', 'Mary', 'Manager', '+************', 'active'),
('analyst1', '<EMAIL>', '$2y$10$example_hash_4', 'Peter', 'Analyst', '+************', 'active'),
('coordinator1', '<EMAIL>', '$2y$10$example_hash_5', 'Sarah', 'Coordinator', '+************', 'active');

-- Insert sample branches
INSERT INTO `branches` (`name`, `code`, `location`, `address`, `phone`, `email`, `status`) VALUES
('Head Office', 'HO', 'Port Moresby', 'Waigani, National Capital District', '+************', '<EMAIL>', 'active'),
('Central Province Branch', 'CPB', 'Kerema', 'Kerema Town, Central Province', '+************', '<EMAIL>', 'active'),
('Western Province Branch', 'WPB', 'Daru', 'Daru Town, Western Province', '+************', '<EMAIL>', 'active'),
('Gulf Province Branch', 'GPB', 'Kerema', 'Kerema District, Gulf Province', '+************', '<EMAIL>', 'active'),
('Highlands Regional Office', 'HRO', 'Mount Hagen', 'Mount Hagen, Western Highlands', '+************', '<EMAIL>', 'active');

-- Insert sample application users
INSERT INTO `users` (`username`, `email`, `password_hash`, `first_name`, `last_name`, `role`, `branch_id`, `status`) VALUES
('project_manager', '<EMAIL>', '$2y$10$example_hash_6', 'James', 'Project', 'Project Manager', 1, 'active'),
('field_officer1', '<EMAIL>', '$2y$10$example_hash_7', 'Grace', 'Field', 'Field Officer', 2, 'active'),
('data_analyst', '<EMAIL>', '$2y$10$example_hash_8', 'David', 'Data', 'Data Analyst', 1, 'active'),
('regional_coord', '<EMAIL>', '$2y$10$example_hash_9', 'Ruth', 'Regional', 'Regional Coordinator', 5, 'active'),
('finance_officer', '<EMAIL>', '$2y$10$example_hash_10', 'Michael', 'Finance', 'Finance Officer', 1, 'active');

-- Insert sample commodities
INSERT INTO `commodities` (`name`, `commodity_code`, `category`, `subcategory`, `unit_of_measure`, `description`, `seasonal_pattern`, `status`) VALUES
('Coffee Arabica', 'COF_ARA', 'Cash Crops', 'Tree Crops', 'kg', 'High quality arabica coffee beans', 'April-September harvest', 'active'),
('Cocoa Beans', 'COC_BEA', 'Cash Crops', 'Tree Crops', 'kg', 'Dried cocoa beans for export', 'Year round harvest', 'active'),
('Sweet Potato', 'SWE_POT', 'Food Crops', 'Root Crops', 'kg', 'Highland sweet potato varieties', 'Year round', 'active'),
('Taro', 'TAR_ROO', 'Food Crops', 'Root Crops', 'kg', 'Traditional taro varieties', 'Wet season', 'active'),
('Vanilla Beans', 'VAN_BEA', 'Spices', 'Tree Crops', 'kg', 'Premium vanilla beans', 'June-August harvest', 'active'),
('Black Pepper', 'BLA_PEP', 'Spices', 'Vine Crops', 'kg', 'Dried black pepper', 'Year round', 'active'),
('Coconut', 'COC_NUT', 'Tree Crops', 'Nuts', 'nut', 'Fresh coconuts', 'Year round', 'active'),
('Oil Palm', 'OIL_PAL', 'Industrial Crops', 'Tree Crops', 'kg', 'Fresh fruit bunches', 'Year round', 'active');

-- Insert sample commodity prices
INSERT INTO `commodity_prices` (`commodity_id`, `price`, `currency`, `market_location`, `price_date`, `price_type`, `quality_grade`, `source`) VALUES
(1, 15.50, 'PGK', 'Goroka Market', '2024-08-01', 'farm_gate', 'Premium', 'Field Survey'),
(1, 18.00, 'PGK', 'Port Moresby', '2024-08-01', 'wholesale', 'Premium', 'Market Survey'),
(2, 12.80, 'PGK', 'Rabaul', '2024-08-01', 'farm_gate', 'Grade A', 'Field Survey'),
(2, 15.20, 'PGK', 'Port Moresby', '2024-08-01', 'export', 'Grade A', 'Export Records'),
(3, 3.50, 'PGK', 'Mount Hagen', '2024-08-01', 'retail', 'Standard', 'Market Survey'),
(4, 4.20, 'PGK', 'Kerema Market', '2024-08-01', 'retail', 'Standard', 'Market Survey'),
(5, 85.00, 'PGK', 'Vanimo', '2024-08-01', 'farm_gate', 'Premium', 'Field Survey'),
(6, 25.50, 'PGK', 'Wewak', '2024-08-01', 'farm_gate', 'Grade A', 'Field Survey');

-- Insert sample commodity production
INSERT INTO `commodity_production` (`commodity_id`, `production_quantity`, `unit_of_measure`, `production_period`, `production_year`, `production_season`, `location`, `area_harvested`, `yield_per_hectare`, `source`, `data_quality`) VALUES
(1, 45000.00, 'kg', 'Annual', 2024, 'Dry Season', 'Eastern Highlands Province', 2500.00, 18.00, 'Agricultural Census', 'surveyed'),
(2, 78000.00, 'kg', 'Annual', 2024, 'Year Round', 'East New Britain Province', 3200.00, 24.38, 'Administrative Records', 'administrative'),
(3, 125000.00, 'kg', 'Annual', 2024, 'Year Round', 'Western Highlands Province', 5000.00, 25.00, 'Sample Survey', 'surveyed'),
(4, 89000.00, 'kg', 'Annual', 2024, 'Wet Season', 'Central Province', 3500.00, 25.43, 'Field Survey', 'surveyed'),
(5, 1200.00, 'kg', 'Annual', 2024, 'Dry Season', 'Sandaun Province', 150.00, 8.00, 'Field Survey', 'surveyed'),
(6, 850.00, 'kg', 'Annual', 2024, 'Year Round', 'East Sepik Province', 85.00, 10.00, 'Field Survey', 'surveyed');

-- Insert sample SME businesses
INSERT INTO `sme` (`business_name`, `registration_number`, `business_type`, `industry_sector`, `owner_name`, `contact_phone`, `contact_email`, `address`, `establishment_date`, `employee_count`, `annual_revenue`, `status`) VALUES
('Highland Coffee Processors', 'SME001', 'Processing', 'Agriculture', 'John Kila', '+************', '<EMAIL>', 'Goroka, Eastern Highlands', '2018-03-15', 25, 850000.00, 'active'),
('Coastal Cocoa Cooperative', 'SME002', 'Cooperative', 'Agriculture', 'Mary Temu', '+************', '<EMAIL>', 'Rabaul, East New Britain', '2015-07-20', 45, 1200000.00, 'active'),
('Fresh Produce Distributors', 'SME003', 'Distribution', 'Agriculture', 'Peter Namaliu', '+************', '<EMAIL>', 'Port Moresby, NCD', '2020-01-10', 18, 650000.00, 'active'),
('Spice Trading Company', 'SME004', 'Trading', 'Agriculture', 'Grace Mendi', '+************', '<EMAIL>', 'Vanimo, Sandaun', '2019-09-05', 12, 420000.00, 'active'),
('Organic Fertilizer Solutions', 'SME005', 'Manufacturing', 'Agriculture', 'David Waigani', '+************', '<EMAIL>', 'Mount Hagen, WHP', '2021-05-12', 8, 280000.00, 'active');

-- Insert sample meetings
INSERT INTO `meetings` (`title`, `meeting_type`, `description`, `meeting_date`, `location`, `organizer_id`, `attendees_count`, `status`) VALUES
('Annual Agricultural Planning Meeting', 'Planning', 'Annual meeting to discuss agricultural development plans', '2024-09-15 09:00:00', 'AMIS Head Office', 1, 25, 'scheduled'),
('Commodity Price Review', 'Review', 'Monthly review of commodity prices and market trends', '2024-08-20 14:00:00', 'Conference Room A', 2, 12, 'completed'),
('SME Development Workshop', 'Workshop', 'Workshop on SME development in agriculture sector', '2024-08-25 10:00:00', 'Port Moresby Convention Centre', 3, 45, 'scheduled'),
('Regional Coordination Meeting', 'Coordination', 'Quarterly regional coordination meeting', '2024-09-05 13:00:00', 'Highlands Regional Office', 5, 18, 'scheduled'),
('Budget Planning Session', 'Planning', 'Annual budget planning for next fiscal year', '2024-10-10 09:00:00', 'Ministry of Agriculture', 1, 15, 'scheduled');

-- Insert sample workplans
INSERT INTO `workplans` (`workplan_name`, `workplan_code`, `description`, `workplan_type`, `start_date`, `end_date`, `budget_allocated`, `responsible_officer_id`, `department`, `priority_level`, `status`, `created_by`) VALUES
('Agricultural Development Program 2024', 'ADP_2024', 'Comprehensive agricultural development program', 'Development', '2024-01-01', '2024-12-31', 2500000.00, 1, 'Planning Department', 'high', 'active', 1),
('SME Support Initiative', 'SSI_2024', 'Support program for small and medium enterprises', 'Support', '2024-03-01', '2024-12-31', 800000.00, 2, 'SME Development', 'medium', 'active', 2),
('Market Information System', 'MIS_2024', 'Development of market information system', 'Technology', '2024-06-01', '2025-05-31', 1200000.00, 3, 'Information Systems', 'high', 'active', 3),
('Capacity Building Program', 'CBP_2024', 'Training and capacity building for farmers', 'Training', '2024-02-01', '2024-11-30', 600000.00, 4, 'Extension Services', 'medium', 'active', 4),
('Infrastructure Development', 'ID_2024', 'Rural infrastructure development project', 'Infrastructure', '2024-04-01', '2025-03-31', 3500000.00, 5, 'Infrastructure', 'high', 'active', 5);

-- Insert sample organization settings
INSERT INTO `org_settings` (`setting_key`, `setting_value`, `description`) VALUES
('organization_name', 'Agricultural Management Information System', 'Official organization name'),
('fiscal_year_start', '01-01', 'Fiscal year start date (MM-DD)'),
('default_currency', 'PGK', 'Default currency for financial transactions'),
('working_days_per_week', '5', 'Standard working days per week'),
('annual_leave_days', '21', 'Annual leave entitlement in days'),
('budget_approval_threshold', '50000', 'Budget approval threshold amount'),
('document_retention_years', '7', 'Document retention period in years'),
('backup_frequency', 'daily', 'Database backup frequency');

-- =====================================================
-- END OF SCHEMA AND DATA
-- =====================================================

-- Summary of created tables:
-- 1. Administrative/Reference Tables: 4 tables (adx_country, gov_structure, regions, region_province_link)
-- 2. User Management Tables: 4 tables (dakoii_users, users, branches, org_settings)
-- 3. SME Management Tables: 2 tables (sme, sme_staff)
-- 4. Document Management Tables: 2 tables (folders, documents)
-- 5. Commodities Tables: 3 tables (commodities, commodity_prices, commodity_production)
-- 6. Meeting/Agreement Tables: 3 tables (meetings, agreements, proposal)
-- 7. Planning Tables: 8 tables (plans_corporate_plan, plans_mtdp, plans_mtdp_dip, plans_mtdp_kra, plans_mtdp_spa, plans_mtdp_specific_area, plans_mtdp_strategies, plans_mtdp_indicators, plans_mtdp_investments, plans_nasp)
-- 8. Workplan Management Tables: 6 tables (workplans, workplan_activities, workplan_infrastructure_activities, workplan_input_activities, workplan_output_activities, workplan_training_activities)
-- 9. Workplan Linking Tables: 4 tables (workplan_corporate_plan_link, workplan_mtdp_link, workplan_nasp_link, workplan_others_link)
-- 10. System Tables: 1 table (migrations)

-- Total: 39 tables with comprehensive sample data
-- Database ready for agricultural management information system
